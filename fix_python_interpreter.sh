#!/bin/bash

# 修复PyCharm Python解释器问题
# 解决libpython3.12.dylib库文件路径问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 修复Python解释器问题 ===${NC}"
echo ""

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CURA_DIR="$PROJECT_ROOT/Cura"
VENV_DIR="$CURA_DIR/build/generators/cura_venv"
PYTHON_BIN="$VENV_DIR/bin/python3.12"

# 检查文件是否存在
if [ ! -f "$PYTHON_BIN" ]; then
    echo -e "${RED}错误: Python解释器不存在: $PYTHON_BIN${NC}"
    exit 1
fi

echo -e "${BLUE}1. 查找libpython3.12.dylib库文件...${NC}"

# 查找Conan中的Python库文件
PYTHON_LIB=$(find /Users/<USER>/.conan2 -name "libpython3.12.dylib" -path "*/p/lib/*" | head -1)

if [ -z "$PYTHON_LIB" ]; then
    echo -e "${RED}错误: 无法找到libpython3.12.dylib库文件${NC}"
    exit 1
fi

echo -e "${GREEN}找到Python库文件: $PYTHON_LIB${NC}"

echo -e "${BLUE}2. 创建库文件符号链接...${NC}"

# 创建lib目录（如果不存在）
mkdir -p "$VENV_DIR/lib"

# 创建符号链接
if [ -f "$VENV_DIR/lib/libpython3.12.dylib" ]; then
    echo -e "${YELLOW}库文件已存在，删除旧的符号链接...${NC}"
    rm -f "$VENV_DIR/lib/libpython3.12.dylib"
fi

ln -s "$PYTHON_LIB" "$VENV_DIR/lib/libpython3.12.dylib"
echo -e "${GREEN}✓ 创建符号链接成功${NC}"

echo -e "${BLUE}3. 验证修复结果...${NC}"

# 测试Python解释器
if "$PYTHON_BIN" -c "import sys; print('Python版本:', sys.version)" 2>/dev/null; then
    echo -e "${GREEN}✓ Python解释器修复成功！${NC}"
else
    echo -e "${RED}✗ Python解释器仍有问题${NC}"
    
    # 尝试使用install_name_tool修复rpath
    echo -e "${BLUE}4. 尝试修复rpath...${NC}"
    
    # 获取Python库所在目录
    PYTHON_LIB_DIR=$(dirname "$PYTHON_LIB")
    
    # 修复rpath
    install_name_tool -add_rpath "$PYTHON_LIB_DIR" "$PYTHON_BIN" 2>/dev/null || true
    
    # 再次测试
    if "$PYTHON_BIN" -c "import sys; print('Python版本:', sys.version)" 2>/dev/null; then
        echo -e "${GREEN}✓ rpath修复成功！${NC}"
    else
        echo -e "${RED}✗ 修复失败，建议使用系统Python解释器${NC}"
        
        # 提供系统Python路径作为备选方案
        SYSTEM_PYTHON=$(which python3.12 2>/dev/null || which python3 2>/dev/null || echo "")
        if [ -n "$SYSTEM_PYTHON" ]; then
            echo -e "${YELLOW}建议使用系统Python解释器: $SYSTEM_PYTHON${NC}"
        fi
        exit 1
    fi
fi

echo ""
echo -e "${GREEN}=== 修复完成 ===${NC}"
echo ""
echo -e "${BLUE}现在可以在PyCharm中使用以下Python解释器:${NC}"
echo -e "${GREEN}$PYTHON_BIN${NC}"
echo ""
echo -e "${BLUE}PyCharm配置步骤:${NC}"
echo "1. 打开 PyCharm > Preferences > Project > Python Interpreter"
echo "2. 点击齿轮图标 > Add..."
echo "3. 选择 'Existing environment'"
echo "4. 设置解释器路径为: $PYTHON_BIN"
echo "5. 点击 OK"
echo ""
echo -e "${YELLOW}注意: 如果仍有问题，请重启PyCharm后再试${NC}"
