# Cura 并行开发环境搭建说明

## 概述
本文档记录了在macOS系统上搭建Cura、CuraEngine和Uranium三个项目并行开发环境的完整过程。

## 系统要求
- macOS 11 或更高版本
- Xcode 12 或更高版本
- Python 3.12 或更高版本
- CMake 3.23 或更高版本
- Ninja 1.10 或更高版本
- Conan 2.7.0 (严格版本要求)
- Git

## 环境准备

### 1. 验证系统依赖
```bash
# 检查Python版本
python3 --version  # 应该是3.12+

# 检查CMake版本
cmake --version    # 应该是3.23+

# 检查Git版本
git --version
```

### 2. 安装正确的Conan版本
由于Cura项目对Conan版本有严格要求，我们需要安装Conan 2.7.0：

```bash
# 卸载现有的Conan版本
brew uninstall conan  # 如果通过brew安装的话

# 使用pipx安装指定版本的Conan
pipx install conan==2.7.0 --force

# 验证版本
conan --version  # 应该显示2.7.0
```

## 项目克隆和配置

### 3. 创建项目根目录
```bash
# 在当前目录下创建三个项目的克隆
mkdir -p CuraProject
cd CuraProject
```

### 4. 克隆三个项目
按照官方文档建议，将三个项目克隆到同一个根目录下：

```bash
# 克隆Cura主项目
git clone https://github.com/Ultimaker/Cura.git

# 克隆Uranium项目
git clone https://github.com/Ultimaker/Uranium.git

# 克隆CuraEngine项目
git clone https://github.com/Ultimaker/CuraEngine.git
```

### 5. 配置Conan
只需要为Cura项目配置一次Conan：

```bash
# 进入Cura目录
cd Cura

# 配置Conan
conan config install https://github.com/ultimaker/conan-config.git
conan profile detect --force
```

## 开发环境初始化

### 6. 初始化Cura开发环境
在Cura根目录下执行：

```bash
# 安装依赖并生成PyCharm配置
conan install . --build=missing --update -g VirtualPythonEnv -g PyCharmRunEnv -c user.generator.virtual_python_env:dev_tools=True
```

参数说明：
- `--build=missing`: 构建缺失的依赖包
- `--update`: 下载最新版本的依赖
- `-g VirtualPythonEnv`: 生成Python虚拟环境
- `-g PyCharmRunEnv`: 生成PyCharm运行配置
- `-c user.generator.virtual_python_env:dev_tools=True`: 安装开发工具如pytest

### 7. 配置Uranium并行开发
```bash
# 进入Uranium目录
cd ../Uranium

# 将Uranium设置为可编辑模式
conan editable add . uranium/5.4.0@dev/testing

# 返回Cura目录
cd ../Cura

# 重新安装Cura依赖，使用本地Uranium
conan install . --build=missing --update -g VirtualPythonEnv -g PyCharmRunEnv --require-override=uranium/5.4.0@dev/testing -c user.generator.virtual_python_env:dev_tools=True
```

### 8. 配置CuraEngine并行开发
```bash
# 进入CuraEngine目录
cd ../CuraEngine

# 构建CuraEngine (Debug模式便于调试)
conan install . --build=missing --update
conan build . --build=missing
```

## PyCharm IDE配置

### 9. PyCharm项目设置
1. 打开PyCharm
2. 打开Cura项目目录
3. 在PyCharm中，选择 File > Open，然后选择Uranium目录
4. 在弹出的对话框中选择"Attach"，这样可以在同一个窗口中同时开发两个项目

### 10. PyCharm运行配置
Conan生成的PyCharm配置文件位于：
- `build/generators/` 目录下
- 在PyCharm中点击 "Add Configuration..."
- 点击 "Apply" 使配置生效

### 11. Python解释器配置
设置PyCharm使用Conan生成的虚拟环境：
- 路径：`build/generators/cura_venv/bin/python`

## 运行和测试

### 12. 激活虚拟环境
```bash
# 在Cura目录下激活虚拟环境
source build/generators/virtual_python_env.sh
```

### 13. 运行Cura
```bash
# 在激活的虚拟环境中运行
python cura_app.py
```

### 14. 测试Uranium并行开发
```bash
# 删除Uranium中的一个文件来测试是否生效
# rm ../Uranium/UM/Application.py
# 如果Cura启动失败，说明并行开发配置成功
```

### 15. 测试CuraEngine并行开发
```bash
# 使用外部后端模式运行Cura
python cura_app.py --external-backend

# 在另一个终端中连接CuraEngine
cd ../CuraEngine
./build/Release/CuraEngine connect 127.0.0.1:49674
```

## 常见问题和解决方案

### Conan相关问题
1. 版本不匹配：确保使用Conan 2.7.0
2. 权限问题：不要使用sudo运行conan命令
3. 缓存问题：使用 `conan remove "*" -f` 清理缓存

### PyCharm配置问题
1. 配置文件未生成：确保使用了 `-g PyCharmRunEnv` 参数
2. Python解释器路径错误：检查虚拟环境路径是否正确

### 并行开发问题
1. Uranium更改未生效：检查editable模式是否正确设置
2. CuraEngine连接失败：检查端口号是否正确

## 开发工作流程

### 日常开发
1. 激活虚拟环境
2. 在PyCharm中同时编辑多个项目
3. 使用PyCharm的运行配置启动Cura
4. 根据需要启动外部CuraEngine进行调试

### 版本控制
- 每个项目都有独立的Git仓库
- 可以在PyCharm中同时为多个项目创建分支
- 建议保持项目间的版本同步

## PyCharm IDE详细配置

### 16. 打开PyCharm项目
1. 启动PyCharm
2. 选择 "Open" 并选择Cura项目目录
3. 等待PyCharm索引完成

### 17. 配置Python解释器
1. 打开 PyCharm > Preferences (macOS) 或 File > Settings (Windows/Linux)
2. 导航到 Project > Python Interpreter
3. 点击齿轮图标 > Add...
4. 选择 "Existing environment"
5. 设置解释器路径为：`/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/cura_venv/bin/python`
6. 点击 OK

### 18. 导入PyCharm运行配置
1. 在PyCharm中，选择 Run > Edit Configurations...
2. 点击左上角的 "+" 号
3. 选择 "Python"
4. 或者直接导入已生成的配置文件：
   - 复制 `.run/` 目录下的 `.xml` 文件到 `.idea/runConfigurations/` 目录
   - 重启PyCharm

### 19. 添加Uranium项目到同一工作空间
1. 在PyCharm中，选择 File > Open
2. 选择Uranium目录
3. 在弹出的对话框中选择 "Attach to current project"
4. 这样可以在同一个PyCharm窗口中同时编辑Cura和Uranium

### 20. 配置项目结构
1. 打开 File > Project Structure
2. 确保Cura和Uranium都被标记为源代码根目录
3. 如果需要，可以将CuraEngine目录也添加进来

## 测试并行开发环境

### 21. 测试Cura基本运行
```bash
cd Cura
source build/generators/virtual_python_env.sh
python cura_app.py
```

### 22. 测试Uranium并行开发
为了验证Uranium的并行开发是否正常工作，可以进行以下测试：

```bash
# 在Uranium目录中创建一个测试文件
cd Uranium
echo "# Test file for parallel development" > test_parallel.py

# 在Cura虚拟环境中检查是否能导入
cd ../Cura
source build/generators/virtual_python_env.sh
python -c "import UM; print('Uranium import successful')"
```

### 23. 测试CuraEngine外部后端模式
```bash
# 终端1：启动Cura（外部后端模式）
cd Cura
source build/generators/virtual_python_env.sh
python cura_app.py --external-backend

# 终端2：启动CuraEngine连接到Cura
cd CuraEngine
./build/Release/CuraEngine connect 127.0.0.1:49674
```

### 24. 在PyCharm中运行和调试
1. 使用导入的运行配置直接在PyCharm中启动Cura
2. 设置断点进行调试
3. 可以同时调试Cura和Uranium代码

## 常见问题解决

### Python解释器问题
如果PyCharm显示Python解释器无效：
1. 检查虚拟环境路径是否正确
2. 重新激活虚拟环境：`source build/generators/virtual_python_env.sh`
3. 在PyCharm中重新选择解释器

### 模块导入错误
如果出现模块导入错误：
1. 确保Uranium已正确设置为可编辑模式：`conan editable list`
2. 检查PYTHONPATH是否包含正确的路径
3. 重新运行 `conan install` 命令

### CuraEngine连接问题
如果CuraEngine无法连接到Cura：
1. 确保Cura以外部后端模式启动
2. 检查端口号是否正确（默认49674）
3. 检查防火墙设置

### 构建错误
如果遇到构建错误：
1. 清理Conan缓存：`conan remove "*" -f`
2. 重新运行完整的安装过程
3. 检查Xcode命令行工具是否正确安装

## 开发工作流程建议

### 日常开发
1. 使用PyCharm同时编辑多个项目
2. 利用PyCharm的版本控制功能管理多个Git仓库
3. 使用PyCharm的运行配置快速启动和调试

### 代码提交
1. 分别为每个项目创建分支
2. 确保修改在各自的项目中测试通过
3. 按项目分别提交代码

### 版本同步
1. 定期更新所有三个项目到最新版本
2. 注意项目间的版本兼容性
3. 如有必要，更新Conan依赖版本

## 注意事项
1. 首次构建可能需要30分钟以上
2. 确保所有项目都在同一个根目录下
3. Conan配置只需要做一次
4. 每次更新依赖时需要重新运行conan install命令
5. 保持Conan 2.7.0版本，不要随意升级
6. 定期备份开发环境配置
