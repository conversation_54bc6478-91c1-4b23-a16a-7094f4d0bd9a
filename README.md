# Cura 并行开发环境

这是一个完整的Cura、CuraEngine和Uranium并行开发环境，按照官方文档搭建，支持PyCharm IDE开发。

## 🎯 环境概述

- **Cura**: 主应用程序，3D打印切片软件前端
- **Uranium**: Cura的核心框架库
- **CuraEngine**: 后端切片引擎
- **开发工具**: PyCharm IDE + Conan 2.7.0 + Python 3.12

## 📁 项目结构

```
CuraProject/
├── Cura/                          # Cura主项目
│   ├── build/generators/          # Conan生成的文件
│   │   ├── cura_venv/             # Python虚拟环境
│   │   └── virtual_python_env.sh  # 虚拟环境激活脚本
│   └── .run/                      # PyCharm运行配置
├── Uranium/                       # Uranium框架（可编辑模式）
├── CuraEngine/                    # CuraEngine后端
│   └── build/Release/CuraEngine   # 编译后的可执行文件
├── start_cura_dev.sh              # 开发环境启动脚本
├── setup_pycharm.sh               # PyCharm配置脚本
└── Cura开发环境搭建说明.md         # 详细搭建文档
```

## 🚀 快速开始

### 1. 检查环境状态
```bash
./start_cura_dev.sh --status
```

### 2. 启动Cura（普通模式）
```bash
./start_cura_dev.sh
```

### 3. 启动Cura（外部后端模式）
```bash
# 终端1
./start_cura_dev.sh --external-backend

# 终端2
./start_cura_dev.sh --engine
```

### 4. 配置PyCharm
```bash
./setup_pycharm.sh
```

然后在PyCharm中打开Cura目录即可开始开发。

## 🛠️ 开发环境特性

### ✅ 已配置功能
- [x] Conan 2.7.0 依赖管理
- [x] Python 3.12 虚拟环境
- [x] Uranium可编辑模式（并行开发）
- [x] CuraEngine独立构建
- [x] PyCharm运行配置自动生成
- [x] 外部后端调试模式
- [x] 一键启动脚本

### 🔧 核心组件版本
- **Conan**: 2.7.0（严格版本要求）
- **Python**: 3.12.2
- **Cura**: 5.11.0-alpha.0
- **Uranium**: 5.11.0-alpha.0（可编辑模式）
- **CuraEngine**: 5.11.0-alpha.0

## 📖 使用指南

### 日常开发工作流

1. **启动开发环境**
   ```bash
   ./start_cura_dev.sh
   ```

2. **在PyCharm中开发**
   - 同时编辑Cura和Uranium代码
   - 使用内置运行配置启动和调试
   - 实时看到Uranium修改的效果

3. **调试后端引擎**
   ```bash
   # 启动外部后端模式
   ./start_cura_dev.sh --external-backend
   
   # 在另一个终端启动CuraEngine
   ./start_cura_dev.sh --engine
   ```

### 测试并行开发

验证Uranium并行开发是否正常工作：

```bash
# 在Uranium中做一个小修改
echo "# Test modification" >> Uranium/test_file.py

# 重启Cura，修改应该立即生效
./start_cura_dev.sh
```

### 更新依赖

当需要更新依赖时：

```bash
cd Cura
conan install . --build=missing --update -g VirtualPythonEnv -g PyCharmRunEnv
```

## 🔍 故障排除

### 常见问题

1. **Python解释器路径无效**
   ```bash
   # 重新激活虚拟环境
   cd Cura
   source build/generators/virtual_python_env.sh
   ```

2. **Uranium修改未生效**
   ```bash
   # 检查可编辑模式
   conan editable list
   
   # 重新设置可编辑模式
   cd Uranium
   conan editable add . --name=uranium --version=5.11.0-alpha.0
   ```

3. **CuraEngine连接失败**
   - 确保Cura以`--external-backend`模式启动
   - 检查端口49674是否被占用
   - 检查防火墙设置

4. **构建错误**
   ```bash
   # 清理Conan缓存
   conan remove "*" -f
   
   # 重新构建
   cd Cura
   conan install . --build=missing -g VirtualPythonEnv -g PyCharmRunEnv
   ```

### 获取帮助

```bash
# 查看启动脚本帮助
./start_cura_dev.sh --help

# 查看环境状态
./start_cura_dev.sh --status
```

## 📚 文档

- [详细搭建说明](./Cura开发环境搭建说明.md) - 完整的环境搭建步骤
- [官方文档](https://github.com/Ultimaker/Cura/wiki/Running-Cura-from-Source) - Ultimaker官方开发文档

## ⚠️ 重要注意事项

1. **Conan版本**: 必须使用2.7.0版本，不要随意升级
2. **项目位置**: 三个项目必须在同一个根目录下
3. **虚拟环境**: 只使用Conan生成的虚拟环境，不要创建其他虚拟环境
4. **并行开发**: Uranium的修改会立即影响Cura，无需重新安装

## 🎉 开发愉快！

现在您已经拥有了一个完整的Cura并行开发环境，可以：
- 同时开发Cura前端和Uranium框架
- 调试CuraEngine后端
- 使用PyCharm进行高效开发
- 快速测试和验证修改

如有问题，请参考详细文档或检查环境状态。
