#!/bin/bash

# PyCharm 配置设置脚本
# 用于自动配置PyCharm的Python解释器和运行配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CURA_DIR="$PROJECT_ROOT/Cura"
URANIUM_DIR="$PROJECT_ROOT/Uranium"

echo -e "${BLUE}=== PyCharm 配置设置脚本 ===${NC}"
echo ""

# 检查目录
if [ ! -d "$CURA_DIR" ]; then
    echo -e "${RED}错误: Cura目录不存在: $CURA_DIR${NC}"
    exit 1
fi

if [ ! -d "$URANIUM_DIR" ]; then
    echo -e "${RED}错误: Uranium目录不存在: $URANIUM_DIR${NC}"
    exit 1
fi

# 创建.idea目录（如果不存在）
IDEA_DIR="$CURA_DIR/.idea"
if [ ! -d "$IDEA_DIR" ]; then
    echo -e "${BLUE}创建.idea目录...${NC}"
    mkdir -p "$IDEA_DIR"
fi

# 创建runConfigurations目录
RUN_CONFIG_DIR="$IDEA_DIR/runConfigurations"
if [ ! -d "$RUN_CONFIG_DIR" ]; then
    echo -e "${BLUE}创建runConfigurations目录...${NC}"
    mkdir -p "$RUN_CONFIG_DIR"
fi

# 复制运行配置文件
echo -e "${BLUE}复制PyCharm运行配置...${NC}"
if [ -d "$CURA_DIR/.run" ]; then
    cp "$CURA_DIR/.run"/*.xml "$RUN_CONFIG_DIR/" 2>/dev/null || true
    echo -e "${GREEN}✓ 运行配置已复制${NC}"
else
    echo -e "${YELLOW}警告: .run目录不存在，请先运行conan install生成PyCharm配置${NC}"
fi

# 获取Python解释器路径
PYTHON_PATH="$CURA_DIR/build/generators/cura_venv/bin/python"
if [ ! -f "$PYTHON_PATH" ]; then
    echo -e "${RED}错误: Python解释器不存在: $PYTHON_PATH${NC}"
    echo -e "${YELLOW}请先运行conan install生成虚拟环境${NC}"
    exit 1
fi

# 创建misc.xml配置文件
echo -e "${BLUE}配置项目设置...${NC}"
cat > "$IDEA_DIR/misc.xml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectRootManager" version="2" project-jdk-name="Python 3.12 (Cura)" project-jdk-type="Python SDK" />
</project>
EOF

# 创建modules.xml配置文件
cat > "$IDEA_DIR/modules.xml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://\$PROJECT_DIR\$/.idea/Cura.iml" filepath="\$PROJECT_DIR\$/.idea/Cura.iml" />
      <module fileurl="file://\$PROJECT_DIR\$/../Uranium/.idea/Uranium.iml" filepath="\$PROJECT_DIR\$/../Uranium/.idea/Uranium.iml" />
    </modules>
  </component>
</project>
EOF

# 创建Cura.iml模块文件
cat > "$IDEA_DIR/Cura.iml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://\$MODULE_DIR\$">
      <sourceFolder url="file://\$MODULE_DIR\$" isTestSource="false" />
      <excludeFolder url="file://\$MODULE_DIR\$/build" />
      <excludeFolder url="file://\$MODULE_DIR\$/.conan" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="module" module-name="Uranium" />
  </component>
</module>
EOF

# 为Uranium创建.idea目录和模块文件
URANIUM_IDEA_DIR="$URANIUM_DIR/.idea"
if [ ! -d "$URANIUM_IDEA_DIR" ]; then
    mkdir -p "$URANIUM_IDEA_DIR"
fi

cat > "$URANIUM_IDEA_DIR/Uranium.iml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://\$MODULE_DIR\$">
      <sourceFolder url="file://\$MODULE_DIR\$" isTestSource="false" />
      <excludeFolder url="file://\$MODULE_DIR\$/build" />
      <excludeFolder url="file://\$MODULE_DIR\$/.conan" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>
EOF

# 创建workspace.xml（包含Python解释器配置）
echo -e "${BLUE}配置Python解释器...${NC}"
cat > "$IDEA_DIR/workspace.xml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PropertiesComponent">
    <property name="settings.editor.selected.configurable" value="com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable" />
  </component>
  <component name="RunManager">
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <module name="Cura" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="$PYTHON_PATH" />
      <option name="WORKING_DIRECTORY" value="\$PROJECT_DIR\$" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
  </component>
</project>
EOF

echo -e "${GREEN}✓ PyCharm配置完成${NC}"
echo ""
echo -e "${BLUE}下一步操作:${NC}"
echo "1. 打开PyCharm"
echo "2. 选择 'Open' 并选择Cura目录: $CURA_DIR"
echo "3. 等待PyCharm索引完成"
echo "4. 检查Python解释器是否正确设置为: $PYTHON_PATH"
echo "5. 运行配置应该已经自动导入"
echo ""
echo -e "${YELLOW}注意: 如果Python解释器未正确设置，请手动配置:${NC}"
echo "  - 打开 PyCharm > Preferences > Project > Python Interpreter"
echo "  - 选择 'Existing environment'"
echo "  - 设置路径为: $PYTHON_PATH"
echo ""
echo -e "${GREEN}配置完成！现在可以在PyCharm中开发Cura了。${NC}"
