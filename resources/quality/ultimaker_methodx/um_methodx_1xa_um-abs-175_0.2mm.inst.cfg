[general]
definition = ultimaker_methodx
name = Fast
version = 4

[metadata]
material = ultimaker_abs_175
quality_type = draft
setting_version = 25
type = quality
variant = 1XA
weight = -2

[values]
cool_fan_enabled = =extruder_nr == support_extruder_nr
cool_fan_speed = 85.0
cool_fan_speed_0 = 0
cool_fan_speed_max = 100
cool_min_layer_time = 10
cool_min_layer_time_fan_speed_max = 15
cool_min_speed = =round(speed_wall_0 * 3 / 4) if cool_lift_head else round(speed_wall_0 / 5)
material_final_print_temperature = 235
material_initial_print_temperature = 235
material_print_temperature_layer_0 = 250
raft_airgap = =0.15 if extruder_nr == support_extruder_nr else 0
raft_base_speed = 10
raft_base_thickness = =0.6 if extruder_nr == support_extruder_nr else 0.8
raft_surface_speed = 90
retraction_amount = 0.5
retraction_min_travel = 3.2
skin_overlap = 10
small_skin_width = 3.6
speed_layer_0 = =speed_print * 7/24
speed_prime_tower = =speed_print * 0.25
speed_print = 120.0
speed_roofing = =speed_print * 13/24
speed_support = =speed_print * 5/6
speed_support_interface = =speed_print * 15/24
speed_topbottom = =speed_print * 11/24
speed_wall = =speed_print * 5/24
speed_wall_0 = =speed_print * 1/6
speed_wall_x = =speed_wall
support_angle = 50
support_bottom_density = 24
support_bottom_distance = =layer_height
support_bottom_enable = False
support_bottom_line_width = 0.6
support_bottom_stair_step_height = 0
support_infill_angles = [45,45,45,45,45,45,45,45,45,45,135,135,135,135,135,135,135,135,135,135]
support_infill_rate = 15.0
support_line_width = 0.3
support_material_flow = =0.85*material_flow
support_roof_density = 85
support_supported_skin_fan_speed = 60.0
support_top_distance = =support_z_distance
support_xy_distance = 0.35
support_xy_distance_overhang = 0.25
support_xy_overrides_z = xy_overrides_z
support_z_distance = 0.15
top_skin_expand_distance = 2.4
wall_overhang_angle = 30
wall_overhang_speed_factors = [40]

