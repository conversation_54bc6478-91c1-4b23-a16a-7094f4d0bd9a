#!/bin/bash

# Cura 并行开发环境启动脚本
# 使用方法：
#   ./start_cura_dev.sh                    # 启动普通模式的Cura
#   ./start_cura_dev.sh --external-backend # 启动外部后端模式的Cura
#   ./start_cura_dev.sh --engine           # 启动CuraEngine（需要先启动外部后端模式）
#   ./start_cura_dev.sh --help             # 显示帮助信息

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CURA_DIR="$PROJECT_ROOT/Cura"
CURAENGINE_DIR="$PROJECT_ROOT/CuraEngine"
URANIUM_DIR="$PROJECT_ROOT/Uranium"

# 检查目录是否存在
check_directories() {
    echo -e "${BLUE}检查项目目录...${NC}"
    
    if [ ! -d "$CURA_DIR" ]; then
        echo -e "${RED}错误: Cura目录不存在: $CURA_DIR${NC}"
        exit 1
    fi
    
    if [ ! -d "$URANIUM_DIR" ]; then
        echo -e "${RED}错误: Uranium目录不存在: $URANIUM_DIR${NC}"
        exit 1
    fi
    
    if [ ! -d "$CURAENGINE_DIR" ]; then
        echo -e "${RED}错误: CuraEngine目录不存在: $CURAENGINE_DIR${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 所有项目目录存在${NC}"
}

# 检查虚拟环境
check_virtual_env() {
    echo -e "${BLUE}检查虚拟环境...${NC}"
    
    if [ ! -f "$CURA_DIR/build/generators/virtual_python_env.sh" ]; then
        echo -e "${RED}错误: 虚拟环境脚本不存在${NC}"
        echo -e "${YELLOW}请先运行: cd Cura && conan install . --build=missing -g VirtualPythonEnv -g PyCharmRunEnv${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 虚拟环境脚本存在${NC}"
}

# 检查CuraEngine可执行文件
check_curaengine() {
    echo -e "${BLUE}检查CuraEngine...${NC}"
    
    if [ ! -f "$CURAENGINE_DIR/build/Release/CuraEngine" ]; then
        echo -e "${RED}错误: CuraEngine可执行文件不存在${NC}"
        echo -e "${YELLOW}请先运行: cd CuraEngine && conan build . --build=missing${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ CuraEngine可执行文件存在${NC}"
}

# 检查Uranium可编辑模式
check_uranium_editable() {
    echo -e "${BLUE}检查Uranium可编辑模式...${NC}"
    
    if ! conan editable list | grep -q "uranium"; then
        echo -e "${YELLOW}警告: Uranium未设置为可编辑模式${NC}"
        echo -e "${YELLOW}建议运行: cd Uranium && conan editable add . --name=uranium --version=5.11.0-alpha.0${NC}"
    else
        echo -e "${GREEN}✓ Uranium已设置为可编辑模式${NC}"
    fi
}

# 启动普通模式Cura
start_cura() {
    echo -e "${BLUE}启动Cura（普通模式）...${NC}"
    cd "$CURA_DIR"
    source build/generators/virtual_python_env.sh
    echo -e "${GREEN}虚拟环境已激活${NC}"
    echo -e "${GREEN}启动Cura...${NC}"
    python cura_app.py "$@"
}

# 启动外部后端模式Cura
start_cura_external() {
    echo -e "${BLUE}启动Cura（外部后端模式）...${NC}"
    cd "$CURA_DIR"
    source build/generators/virtual_python_env.sh
    echo -e "${GREEN}虚拟环境已激活${NC}"
    echo -e "${GREEN}启动Cura（外部后端模式）...${NC}"
    echo -e "${YELLOW}注意: 请在另一个终端中运行 './start_cura_dev.sh --engine' 来启动CuraEngine${NC}"
    python cura_app.py --external-backend "$@"
}

# 启动CuraEngine
start_curaengine() {
    echo -e "${BLUE}启动CuraEngine...${NC}"
    cd "$CURAENGINE_DIR"
    echo -e "${GREEN}连接到Cura（端口49674）...${NC}"
    ./build/Release/CuraEngine connect 127.0.0.1:49674
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Cura 并行开发环境启动脚本${NC}"
    echo ""
    echo "使用方法："
    echo "  $0                    启动普通模式的Cura"
    echo "  $0 --external-backend 启动外部后端模式的Cura"
    echo "  $0 --engine           启动CuraEngine（需要先启动外部后端模式）"
    echo "  $0 --help             显示此帮助信息"
    echo ""
    echo "开发环境信息："
    echo "  Cura目录:       $CURA_DIR"
    echo "  Uranium目录:    $URANIUM_DIR"
    echo "  CuraEngine目录: $CURAENGINE_DIR"
    echo ""
    echo "注意事项："
    echo "  1. 确保已经运行过完整的环境搭建流程"
    echo "  2. 外部后端模式需要在两个终端中分别启动Cura和CuraEngine"
    echo "  3. 如果遇到问题，请检查虚拟环境和依赖是否正确安装"
}

# 显示环境状态
show_status() {
    echo -e "${BLUE}=== Cura 开发环境状态 ===${NC}"
    echo ""
    
    # 检查Conan版本
    echo -e "${BLUE}Conan版本:${NC}"
    conan --version || echo -e "${RED}Conan未安装${NC}"
    echo ""
    
    # 检查可编辑包
    echo -e "${BLUE}Conan可编辑包:${NC}"
    conan editable list || echo -e "${RED}无可编辑包${NC}"
    echo ""
    
    # 检查Python解释器
    if [ -f "$CURA_DIR/build/generators/virtual_python_env.sh" ]; then
        echo -e "${BLUE}Python解释器:${NC}"
        cd "$CURA_DIR"
        source build/generators/virtual_python_env.sh
        python --version
        which python
        echo ""
    fi
    
    # 检查文件状态
    echo -e "${BLUE}关键文件状态:${NC}"
    [ -f "$CURA_DIR/build/generators/virtual_python_env.sh" ] && echo -e "${GREEN}✓${NC} Cura虚拟环境" || echo -e "${RED}✗${NC} Cura虚拟环境"
    [ -f "$CURAENGINE_DIR/build/Release/CuraEngine" ] && echo -e "${GREEN}✓${NC} CuraEngine可执行文件" || echo -e "${RED}✗${NC} CuraEngine可执行文件"
    [ -d "$CURA_DIR/.run" ] && echo -e "${GREEN}✓${NC} PyCharm运行配置" || echo -e "${RED}✗${NC} PyCharm运行配置"
    echo ""
}

# 主函数
main() {
    case "${1:-}" in
        --help|-h)
            show_help
            ;;
        --status|-s)
            show_status
            ;;
        --external-backend)
            check_directories
            check_virtual_env
            check_uranium_editable
            shift
            start_cura_external "$@"
            ;;
        --engine)
            check_directories
            check_curaengine
            start_curaengine
            ;;
        *)
            check_directories
            check_virtual_env
            check_uranium_editable
            start_cura "$@"
            ;;
    esac
}

# 运行主函数
main "$@"
